/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership. The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.fineract.infrastructure.bulkimport.constants;

public final class LoanConstants {

    private LoanConstants() {

    }

    public static final int OFFICE_NAME_COL = 0;// A
    public static final int LOAN_TYPE_COL = 1;// B
    public static final int CLIENT_NAME_COL = 2;// C
    public static final int CLIENT_EXTERNAL_ID = 3;// D
    public static final int PRODUCT_COL = 4;// E
    public static final int LOAN_OFFICER_NAME_COL = 5;// F
    public static final int SUBMITTED_ON_DATE_COL = 6;// G
    public static final int APPROVED_DATE_COL = 7;// H
    public static final int DISBURSED_DATE_COL = 8;// I
    public static final int DISBURSED_PAYMENT_TYPE_COL = 9;// J
    public static final int FUND_NAME_COL = 10;// K
    public static final int PRINCIPAL_COL = 11;// L
    public static final int NO_OF_REPAYMENTS_COL = 12;// M
    public static final int REPAID_EVERY_COL = 13;// N
    public static final int REPAID_EVERY_FREQUENCY_COL = 14;// O
    public static final int LOAN_TERM_COL = 15;// P
    public static final int LOAN_TERM_FREQUENCY_COL = 16;// Q
    public static final int NOMINAL_INTEREST_RATE_COL = 17;// R
    public static final int NOMINAL_INTEREST_RATE_FREQUENCY_COL = 18;// S
    public static final int AMORTIZATION_COL = 19;// T
    public static final int INTEREST_METHOD_COL = 20;// U
    public static final int INTEREST_CALCULATION_PERIOD_COL = 21;// V
    public static final int ARREARS_TOLERANCE_COL = 22;// W
    public static final int REPAYMENT_STRATEGY_COL = 23;// X
    public static final int GRACE_ON_PRINCIPAL_PAYMENT_COL = 24;// Y
    public static final int GRACE_ON_INTEREST_PAYMENT_COL = 25;// Z
    public static final int GRACE_ON_INTEREST_CHARGED_COL = 26;// AA
    public static final int INTEREST_CHARGED_FROM_COL = 27;// AB
    public static final int FIRST_REPAYMENT_COL = 28;// AC
    public static final int TOTAL_AMOUNT_REPAID_COL = 29;// AD
    public static final int LAST_REPAYMENT_DATE_COL = 30;// AE
    public static final int REPAYMENT_TYPE_COL = 31;// AF
    public static final int INTEREST_PAID_COL = 32;
    public static final int PENALTY_PAID_COL = 33;// AG
    public static final int STATUS_COL = 34;// AH
    public static final int LOAN_ID_COL = 35;// AI
    public static final int FAILURE_REPORT_COL = 36;// AJ
    public static final int EXTERNAL_ID_COL = 37;// AK
    public static final int CHARGE_ID_1 = 38;// AL
    public static final int CHARGE_AMOUNT_1 = 39;// AM
    public static final int CHARGE_DUE_DATE_1 = 40;// AN
    public static final int CHARGE_ID_2 = 41;// AO
    public static final int CHARGE_AMOUNT_2 = 42;// AP
    public static final int CHARGE_DUE_DATE_2 = 43;// AQ
    public static final int GROUP_ID = 44;// AR
    public static final int LOOKUP_CLIENT_NAME_COL = 45;// AS
    public static final int LOOKUP_CLIENT_EXTERNAL_ID = 46;// AT
    public static final int LOOKUP_ACTIVATION_DATE_COL = 47;// AU
    public static final int LINK_ACCOUNT_ID = 48;// AV

    // Variable Interest Rate columns
    public static final int INTEREST_RATE_CHANGE_DATE_COL = 49;// AW
    public static final int INTEREST_RATE_BEFORE_DATE_COL = 50;// AX
    public static final int INTEREST_RATE_AFTER_DATE_COL = 51;// AY

    // Outstanding Balance column for overriding system calculation
    public static final int OUTSTANDING_BALANCE_COL = 52;// BA

    public static final String LOAN_TYPE_INDIVIDUAL = "Individual";
    public static final String LOAN_TYPE_GROUP = "Group";
    public static final String LOAN_TYPE_JLG = "JLG";

}
