/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership. The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.fineract.portfolio.loanaccount.data;

import java.math.BigDecimal;
import java.time.LocalDate;
import org.apache.fineract.infrastructure.core.data.EnumOptionData;
import org.apache.fineract.portfolio.loanaccount.domain.LoanTermVariationType;

public class LoanTermVariationsData implements Comparable<LoanTermVariationsData> {

    @SuppressWarnings("unused")
    private final Long id;
    private final EnumOptionData termType;
    private LocalDate termVariationApplicableFrom;
    private final BigDecimal decimalValue;
    private final LocalDate dateValue;
    private final boolean isSpecificToInstallment;
    private Boolean isProcessed;

    public LoanTermVariationsData(final Long id, final EnumOptionData termType, final LocalDate termVariationApplicableFrom,
            final BigDecimal decimalValue, final LocalDate dateValue, final boolean isSpecificToInstallment) {
        this.id = id;
        this.termType = termType;
        this.termVariationApplicableFrom = termVariationApplicableFrom;
        this.decimalValue = decimalValue;
        this.dateValue = dateValue;
        this.isSpecificToInstallment = isSpecificToInstallment;
    }

    public LoanTermVariationsData(final EnumOptionData termType, final LocalDate termVariationApplicableFrom, final BigDecimal decimalValue,
            LocalDate dateValue, final boolean isSpecificToInstallment) {
        this.id = null;
        this.termType = termType;
        this.termVariationApplicableFrom = termVariationApplicableFrom;
        this.decimalValue = decimalValue;
        this.dateValue = dateValue;
        this.isSpecificToInstallment = isSpecificToInstallment;
    }

    public EnumOptionData getTermType() {
        return this.termType;
    }

    public LoanTermVariationType getTermVariationType() {
        return LoanTermVariationType.fromInt(this.termType.getId().intValue());
    }

    public LocalDate getTermApplicableFrom() {
        return this.termVariationApplicableFrom;
    }

    public BigDecimal getDecimalValue() {
        return this.decimalValue;
    }

    public boolean isApplicable(final LocalDate fromDate, final LocalDate dueDate) {
        // BUG FIX: For interest rate from installment variations, the logic needs to be adjusted
        // to ensure installments due exactly on the rate change date use the "before" rate
        if (getTermVariationType().isInterestRateFromInstallment()) {
            // For interest rate from installment variations, only apply if the change date is AFTER fromDate
            // and BEFORE dueDate (not including dueDate itself)
            return occursOnDayFromAndUpToExclusive(fromDate, dueDate, this.termVariationApplicableFrom);
        } else {
            // For other variations, use the original logic
            return occursOnDayFromAndUpTo(fromDate, dueDate, this.termVariationApplicableFrom);
        }
    }

    private boolean occursOnDayFromAndUpTo(final LocalDate fromNotInclusive, final LocalDate upToInclusive, final LocalDate target) {
        return target != null && target.isAfter(fromNotInclusive) && !target.isAfter(upToInclusive);
    }

    private boolean occursOnDayFromAndUpToExclusive(final LocalDate fromNotInclusive, final LocalDate upToExclusive, final LocalDate target) {
        return target != null && target.isAfter(fromNotInclusive) && target.isBefore(upToExclusive);
    }

    public boolean isApplicable(final LocalDate fromDate) {
        // BUG FIX: For interest rate variations, installments due exactly on the rate change date
        // should use the "before" rate, not the "after" rate, because they represent interest
        // accrued for the period BEFORE the rate change took effect
        if (getTermVariationType().isInterestRateFromInstallment()) {
            // For interest rate from installment variations, only apply to dates AFTER the change date
            return occursAfter(fromDate, this.termVariationApplicableFrom);
        } else {
            // For other variations, use the original logic
            return occursBefore(fromDate, this.termVariationApplicableFrom);
        }
    }

    private boolean occursBefore(final LocalDate date, final LocalDate target) {
        return target != null && !target.isAfter(date);
    }

    private boolean occursAfter(final LocalDate date, final LocalDate target) {
        return target != null && target.isBefore(date);
    }

    public LocalDate getDateValue() {
        return this.dateValue;
    }

    public boolean isSpecificToInstallment() {
        return this.isSpecificToInstallment;
    }

    public Boolean isProcessed() {
        return this.isProcessed == null ? false : this.isProcessed;
    }

    public void setProcessed(Boolean isProcessed) {
        this.isProcessed = isProcessed;
    }

    @Override
    public int compareTo(LoanTermVariationsData o) {
        int comparsion = getTermApplicableFrom().compareTo(o.getTermApplicableFrom());
        if (comparsion == 0) {
            if (o.getTermVariationType().isDueDateVariation() || o.getTermVariationType().isInsertInstallment()) {
                comparsion = 1;
            }
        }
        return comparsion;
    }

    public void setApplicableFromDate(final LocalDate applicableFromDate) {
        this.termVariationApplicableFrom = applicableFromDate;
    }

}
